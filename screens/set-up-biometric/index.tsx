import * as LocalAuthentication from 'expo-local-authentication';
import { router } from 'expo-router';
import { ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/Button';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useBiometricAvailableStatus } from '@/hooks/useBiometricAvailableStatus';
import { useSetupBiometric } from '@/hooks/useSetupBiometric';
import { useTheme } from '@/hooks/useThemeColor';

type Props = {};

export const SetUpBioMetric = (props: Props) => {
  const { styles } = useStyles();

  const { data: biometricStatus } = useBiometricAvailableStatus();
  const { mutateAsync: setupBiometric } = useSetupBiometric();

  const isSupportBiometric = biometricStatus?.bioAvailable;
  const isSupportFingerprint =
    isSupportBiometric && biometricStatus?.supportedTypes?.includes(LocalAuthentication.AuthenticationType.FINGERPRINT);
  const isSupportFaceId =
    isSupportBiometric &&
    !isSupportFingerprint &&
    biometricStatus?.supportedTypes?.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION);
  const biometricEnabled = biometricStatus && biometricStatus?.bioEnrolled && biometricStatus?.bioAvailable;

  const btnSetUpTitle = isSupportBiometric
    ? isSupportFaceId
      ? 'Enable Face ID'
      : 'Enable Fingerprint'
    : 'Not supported';

  const handleSetUpBiometric = async () => {
    if (!biometricStatus || !isSupportBiometric) return;
    const { bioAvailable, bioEnrolled } = biometricStatus;

    if (!bioAvailable || !bioEnrolled) return;
    const biometricResult = await LocalAuthentication.authenticateAsync({
      disableDeviceFallback: true,
    });

    if (biometricResult.success) {
      await setupBiometric();
      router.navigate('/(new-wallet)/set-up-notification');
    }
  };

  const handleMaybeLater = () => {
    router.navigate('/(new-wallet)/set-up-notification');
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView bounces={false} showsVerticalScrollIndicator={false}>
        <Spacer height={81} />

        <Show when={isSupportFaceId}>
          <Icons.Face size={56} />

          <Spacer height={24} />

          <ThemedText type='title'>Enable Face ID</ThemedText>

          <Spacer height={8} />

          <ThemedText type='smallLight'>Add a extra layer of security when accessing your account</ThemedText>
        </Show>

        <Show when={isSupportFingerprint}>
          <Icons.Fingerprint size={56} />

          <Spacer height={24} />

          <ThemedText type='title'>Enable Fingerprint</ThemedText>

          <Spacer height={8} />

          <ThemedText type='smallLight'>Add a extra layer of security when accessing your account</ThemedText>
        </Show>
      </ScrollView>

      <Spacer height={16} />

      <View style={styles.disclaimerContainer}>
        <ThemedText type='smallMedium'>Disclaimer</ThemedText>

        <ThemedText type='tinyLight'>
          We will not share your data or biometric information with any 3rd party. Your information is used locally to
          access your account.
        </ThemedText>
      </View>

      <Spacer height={16} />

      <View style={styles.actions}>
        <CustomButton type='primary' onPress={handleSetUpBiometric} disabled={!biometricEnabled}>
          {btnSetUpTitle}
        </CustomButton>

        <CustomButton type='secondary' onPress={handleMaybeLater}>
          Maybe later
        </CustomButton>
      </View>
    </SafeAreaView>
  );
};

const useStyles = () => {
  const white04 = useTheme('white04');

  const styles = StyleSheet.create({
    disclaimerContainer: {
      backgroundColor: white04,
      borderRadius: 16,
      padding: 16,
    },
    container: {
      flex: 1,
      padding: 16,
    },
    actions: {
      flexDirection: 'column',
      gap: 8,
    },
  });

  return { styles };
};
