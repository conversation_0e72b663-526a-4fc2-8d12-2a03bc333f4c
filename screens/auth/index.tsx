import * as Haptics from 'expo-haptics';
import * as LocalAuthentication from 'expo-local-authentication';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { BackHandler, StyleSheet, View } from 'react-native';
import QuickCrypto from 'react-native-quick-crypto';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NumberKeyboard } from '@/components/NumberKeyboard';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useGetBiometric } from '@/hooks/useGetBiometric';
import { useGetPin } from '@/hooks/useGetPin';
import { useTheme } from '@/hooks/useThemeColor';

type Props = {};

export const Auth = (props: Props) => {
  const [pin, setPin] = useState('');

  const { data: hashingPassCode } = useGetPin();
  const { data: biometricEnabled } = useGetBiometric();

  const { styles } = useStyles();

  const handlePressNum = (num: number) => {
    if (pin.length === 6) return;
    setPin((prev) => prev + num);
  };

  const handlePressBackspace = () => {
    setPin((prev) => prev.slice(0, -1));
  };

  // Disable back button
  useEffect(() => {
    const onBackPress = () => {
      return true;
    };

    BackHandler.addEventListener('hardwareBackPress', onBackPress);
  }, []);

  useEffect(() => {
    if (pin.length !== 6) return;

    (async () => {
      try {
        const hashingPassCodeCompare = QuickCrypto.createHash('sha256').update(pin).digest('hex');
        const isMatch = hashingPassCode === hashingPassCodeCompare;

        if (isMatch) {
          router.back();
        } else {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          setPin('');
        }
      } catch (error) {
        console.error(error);
      }
    })();
  }, [pin, hashingPassCode]);

  useEffect(() => {
    if (!biometricEnabled) return;

    (async () => {
      try {
        const biometricResult = await LocalAuthentication.authenticateAsync({
          disableDeviceFallback: true,
        });

        if (biometricResult.success) {
          router.back();
        }
      } catch (error) {
        console.error(error);
      }
    })();
  }, [biometricEnabled]);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.headerContainer}>
        <ThemedText type='title' style={styles.title}>
          Enter PIN
        </ThemedText>

        <Spacer height={8} />

        <ThemedText type='smallLight' style={styles.description}>
          Enter a secure pin to access your account
        </ThemedText>

        <Spacer height={24} />

        <View>
          <View style={styles.dotContainer}>
            {Array(6)
              .fill(0)
              .map((_, index) => (
                <View key={index} style={[styles.dot, index < pin?.length && styles.dotActive]} />
              ))}
          </View>
        </View>

        <Spacer height={88} />

        <NumberKeyboard onPressNum={handlePressNum} onPressBackspace={handlePressBackspace} />
      </View>
    </SafeAreaView>
  );
};

const useStyles = () => {
  const backgroundColor = useTheme('background');

  const styles = StyleSheet.create({
    dotActive: {
      borderWidth: 0,
      backgroundColor: '#fff',
    },
    dot: {
      width: 18,
      height: 18,
      borderWidth: 1,
      borderColor: '#B7B7C7',
      borderRadius: 999,
      overflow: 'hidden',
    },
    dotContainer: {
      flexDirection: 'row',
      gap: 12,
      justifyContent: 'center',
    },
    container: {
      flex: 1,
      padding: 24,
      paddingTop: 56,
      backgroundColor,
    },
    headerContainer: {
      paddingHorizontal: 16,
    },
    title: {
      textAlign: 'center',
    },
    description: {
      // color: white90,
      textAlign: 'center',
    },
  });

  return { styles };
};
