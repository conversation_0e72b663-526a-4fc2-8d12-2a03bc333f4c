import { Image } from 'expo-image';
import { router } from 'expo-router';
import { StyleSheet, View } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { CustomButton } from '@/components/Button';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { FontWeight } from '@/constants/Colors';
import { useTheme } from '@/hooks/useThemeColor';
import { useCommonStore } from '@/store/common';

type Props = {};

export const Onboarding = (props: Props) => {
  const { styles } = useStyles();
  const setIsFirstTime = useCommonStore.use.setIsFirstTime();

  const handleCreateAccount = () => {
    // TODO: Navigate to create account screen
    setIsFirstTime(false);
    router.navigate('/(new-wallet)/set-up-security');
  };

  const handleSignIn = () => {
    // TODO: Navigate to sign in screen
  };

  return (
    <SafeAreaView style={styles.container}>
      <Image source={require('@/assets/images/onboarding.png')} style={styles.bgImage} />

      <View style={styles.bottom}>
        <ThemedText style={styles.title} type='title'>
          Geo<ThemedText style={[styles.title, styles.safeTitle]}>Safe</ThemedText>
        </ThemedText>

        <Spacer height={16.5} />

        <ThemedText style={styles.terms}>
          A smart multisig wallet secured by your context – not just your keys.
        </ThemedText>

        <Spacer height={61.5} />

        <ThemedText style={styles.terms}>By Proceeding you agree to our</ThemedText>
        <ThemedText style={[styles.terms, styles.termsCondition]}>Terms & Conditions</ThemedText>

        <Spacer height={4} />

        <Spacer height={4} />

        <View style={styles.actions}>
          <CustomButton type='primary' onPress={handleCreateAccount}>
            Create account
          </CustomButton>

          <CustomButton type='secondary' onPress={handleSignIn}>
            Sign in
          </CustomButton>
        </View>
      </View>
    </SafeAreaView>
  );
};

const useStyles = () => {
  const backgroundColor = useTheme('background');
  const primary = useTheme('primary');
  const insets = useSafeAreaInsets();

  const styles = StyleSheet.create({
    termsCondition: {
      color: primary,
      textDecorationStyle: 'solid',
      textDecorationLine: 'underline',
    },
    terms: {
      color: 'rgba(255, 255, 255, 0.8)',
      textAlign: 'center',
    },
    container: {
      flex: 1,
      backgroundColor,
    },
    bgImage: {
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
    },
    title: {
      fontSize: 32,
      ...FontWeight.semiBold,
      textAlign: 'center',
    },
    safeTitle: {
      color: primary,
    },
    bottom: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      paddingBottom: insets.bottom + 16,
      paddingHorizontal: 16,
    },
    actions: {
      flexDirection: 'column',
      gap: 8,
    },
  });

  return { styles };
};
