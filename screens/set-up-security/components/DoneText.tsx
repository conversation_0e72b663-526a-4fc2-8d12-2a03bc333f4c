import { StyleSheet, View } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { FontWeight } from '@/constants/Colors';
import { useTheme } from '@/hooks/useThemeColor';

type Props = {};

export const DoneText = (props: Props) => {
  const { styles } = useStyles();

  return (
    <View style={styles.doneBox}>
      <ThemedText style={styles.doneText}>Done</ThemedText>
    </View>
  );
};

const useStyles = () => {
  const primary = useTheme('primary');

  const styles = StyleSheet.create({
    doneBox: {
      paddingVertical: 2,
      paddingHorizontal: 4,
      backgroundColor: '#0C4539',
      borderRadius: 4,
    },
    doneText: {
      color: primary,
      fontSize: 11,
      ...FontWeight.medium,
      lineHeight: 14,
    },
  });

  return { styles };
};
