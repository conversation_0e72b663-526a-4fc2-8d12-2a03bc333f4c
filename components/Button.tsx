import { ActivityIndicator, StyleSheet, TextStyle, TouchableOpacity, TouchableOpacityProps } from 'react-native';
import { FontWeight } from '@/constants/Colors';
import { useTheme } from '@/hooks/useThemeColor';
import { Spacer } from './Spacer';
import { ThemedText, ThemedTextProps } from './ThemedText';

type Props = TouchableOpacityProps & {
  isLoading?: boolean;
  textStyle?: TextStyle;
  textType?: ThemedTextProps['type'];
  type?: 'primary' | 'secondary';
};

export const CustomButton = ({
  type = 'primary',
  style,
  children,
  isLoading,
  disabled,
  textStyle,
  textType = 'default',
  ...rest
}: Props) => {
  const { styles } = useStyles();

  return (
    <TouchableOpacity
      style={[
        styles.button,
        type === 'primary' ? styles.buttonPrimary : undefined,
        type === 'secondary' ? styles.buttonSecondary : undefined,
        style,
        isLoading || disabled ? { opacity: 0.5 } : undefined,
      ]}
      disabled={disabled || isLoading}
      activeOpacity={0.7}
      {...rest}
    >
      {isLoading && (
        <>
          <ActivityIndicator color='#000' size='small' />
          {children && <Spacer width={16} />}
        </>
      )}
      {typeof children === 'string' ? (
        <ThemedText
          style={[
            styles.buttonText,
            type === 'primary' ? styles.textPrimary : undefined,
            type === 'secondary' ? styles.textSecondary : undefined,
            textStyle,
          ]}
          type={textType}
          numberOfLines={1}
        >
          {children}
        </ThemedText>
      ) : (
        children
      )}
    </TouchableOpacity>
  );
};

const useStyles = () => {
  const primary = useTheme('primary');
  const secondary = useTheme('secondary');
  const textButtonPrimary = useTheme('textButtonPrimary');
  const white65 = useTheme('white65');

  const styles = StyleSheet.create({
    button: {
      paddingHorizontal: 12,
      minHeight: 48,
      borderRadius: 8,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    buttonPrimary: {
      backgroundColor: primary,
    },
    buttonSecondary: {
      backgroundColor: secondary,
    },
    buttonText: {
      textAlign: 'center',
      fontSize: 16,
      ...FontWeight.semiBold,
    },
    textPrimary: {
      color: textButtonPrimary,
    },
    textSecondary: {
      color: white65,
    },
  });

  return { styles };
};
