import { StyleSheet, Text, type TextProps } from 'react-native';
import { FontWeight } from '@/constants/Colors';
import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?:
    | 'default'
    | 'title'
    | 'defaultSemiBold'
    | 'defaultLight'
    | 'subtitle'
    | 'link'
    | 'smallLight'
    | 'small'
    | 'smallMedium'
    | 'tinyLight'
    | 'tinyNormal'
    | 'tinyMedium';
};

export function ThemedText({ style, lightColor, darkColor, type = 'default', ...rest }: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text');

  return (
    <Text
      style={[
        { color },
        type === 'title' ? styles.title : undefined,
        type === 'subtitle' ? styles.subtitle : undefined,
        type === 'defaultLight' ? styles.defaultLight : undefined,
        type === 'default' ? styles.default : undefined,
        type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
        type === 'smallLight' ? styles.smallLight : undefined,
        type === 'small' ? styles.smallNormal : undefined,
        type === 'smallMedium' ? styles.smallMedium : undefined,
        type === 'tinyLight' ? styles.tinyLight : undefined,
        type === 'tinyNormal' ? styles.tinyNormal : undefined,
        type === 'tinyMedium' ? styles.tinyMedium : undefined,
        type === 'link' ? styles.link : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontSize: 16,
    lineHeight: 22,
    ...FontWeight.regular,
  },
  defaultLight: {
    fontSize: 16,
    lineHeight: 22,
    ...FontWeight.light,
  },
  defaultSemiBold: {
    fontSize: 16,
    lineHeight: 24,
    ...FontWeight.semiBold,
  },
  title: {
    fontSize: 28,
    ...FontWeight.semiBold,
    lineHeight: 36,
  },
  subtitle: {
    fontSize: 20,
    ...FontWeight.bold,
  },
  smallLight: {
    fontSize: 14,
    lineHeight: 22,
    ...FontWeight.light,
  },
  smallNormal: {
    fontSize: 14,
    lineHeight: 22,
    ...FontWeight.regular,
  },
  smallMedium: {
    fontSize: 14,
    lineHeight: 22,
    ...FontWeight.medium,
  },
  tinyLight: {
    fontSize: 12,
    lineHeight: 18,
    ...FontWeight.light,
  },
  tinyNormal: {
    fontSize: 12,
    lineHeight: 18,
    ...FontWeight.regular,
  },
  tinyMedium: {
    fontSize: 12,
    lineHeight: 18,
    ...FontWeight.medium,
  },
  link: {
    lineHeight: 30,
    fontSize: 16,
    color: '#0a7ea4',
    ...FontWeight.light,
  },
});
