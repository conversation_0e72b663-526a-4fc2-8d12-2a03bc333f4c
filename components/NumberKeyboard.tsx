import * as Haptics from 'expo-haptics';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Icons } from '@/assets/icons';
import { useTheme } from '@/hooks/useThemeColor';
import { ThemedText } from './ThemedText';

type Props = {
  onPressNum: (num: number) => void;
  onPressBackspace: () => void;
};

export const NumberKeyboard = ({ onPressNum, onPressBackspace }: Props) => {
  const { styles } = useStyles();

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <NumberKeyboardButton num={1} onPress={onPressNum} />
        <NumberKeyboardButton num={2} onPress={onPressNum} />
        <NumberKeyboardButton num={3} onPress={onPressNum} />
      </View>

      <View style={styles.row}>
        <NumberKeyboardButton num={4} onPress={onPressNum} />
        <NumberKeyboardButton num={5} onPress={onPressNum} />
        <NumberKeyboardButton num={6} onPress={onPressNum} />
      </View>

      <View style={styles.row}>
        <NumberKeyboardButton num={7} onPress={onPressNum} />
        <NumberKeyboardButton num={8} onPress={onPressNum} />
        <NumberKeyboardButton num={9} onPress={onPressNum} />
      </View>

      <View style={styles.row}>
        <View style={{ width: 76, height: 76 }} />
        <NumberKeyboardButton num={0} onPress={onPressNum} />
        <BackspaceKeyboardButton onPress={onPressBackspace} />
      </View>
    </View>
  );
};

const NumberKeyboardButton = ({ num, onPress }: { num: number; onPress: (num: number) => void }) => {
  const { styles } = useStyles();

  const handlePress = () => {
    onPress(num);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  return (
    <TouchableOpacity style={styles.numContainer} onPress={handlePress} activeOpacity={0.7}>
      <ThemedText type='title' style={styles.title}>
        {num}
      </ThemedText>
    </TouchableOpacity>
  );
};

const BackspaceKeyboardButton = ({ onPress }: { onPress: () => void }) => {
  const { styles } = useStyles();

  const handlePress = () => {
    onPress();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  return (
    <TouchableOpacity
      style={[styles.numContainer, { backgroundColor: 'transparent' }]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <Icons.BackSpace size={28} />
    </TouchableOpacity>
  );
};

const useStyles = () => {
  const Neutrals800 = useTheme('Neutrals/800');

  const styles = StyleSheet.create({
    numContainer: {
      width: 76,
      height: 76,
      borderRadius: 999,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: Neutrals800,
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      height: 76,
      gap: 24,
    },
    container: {
      flex: 1,
      gap: 24,
    },
    headerContainer: {
      paddingHorizontal: 16,
    },
    title: {
      textAlign: 'center',
    },
    description: {
      // color: white90,
      textAlign: 'center',
    },
  });

  return { styles };
};
