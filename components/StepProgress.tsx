import { StyleSheet, View } from 'react-native';
import { useTheme } from '@/hooks/useThemeColor';
import { Show } from './Show';

type Props = {
  isActive: boolean;
};

export const StepProgress = ({ isActive }: Props) => {
  const { styles } = useStyles();

  return (
    <View style={styles.container}>
      <View style={styles.dot}>
        <Show when={isActive}>
          <View style={styles.outCircle} />
        </Show>
      </View>

      <View style={styles.line} />
    </View>
  );
};

const useStyles = () => {
  const color = '#B7B7C7';
  const white35 = useTheme('white35');
  const primary = useTheme('primary');

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'column',
      alignItems: 'center',
    },
    dot: {
      width: 8,
      height: 8,
      borderRadius: 999,
      backgroundColor: color,
    },
    outCircle: {
      position: 'absolute',
      width: 14,
      height: 14,
      borderRadius: 999,
      borderColor: primary,
      borderWidth: 1,
      transform: [{ translateX: -3 }, { translateY: -3 }],
    },
    line: {
      borderWidth: 1,
      borderRadius: 999,
      borderColor: white35,
      borderStyle: 'dashed',
      flex: 1,
    },
  });

  return { styles };
};
