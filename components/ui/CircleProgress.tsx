import { useEffect, useMemo } from 'react';
import { PixelRatio } from 'react-native';
import Reanimated, { useAnimatedProps, useSharedValue, withTiming } from 'react-native-reanimated';
import Svg, { Circle, Text } from 'react-native-svg';

const CircleAnimated = Reanimated.createAnimatedComponent(Circle);

type Props = {
  percentage: number;
  radius: number;
  borderWidth: number;
  duration?: number;
  innerText?: string;
};

export const CircleProgress = ({ percentage, radius, borderWidth = 10, duration = 500, innerText }: Props) => {
  const loaderRadius = PixelRatio.roundToNearestPixel(radius);
  const innerCircleRadii = loaderRadius - borderWidth / 2;

  const progress = useSharedValue(2 * Math.PI * innerCircleRadii);

  const getCircumferenceData = useMemo(() => {
    const circumference = 2 * Math.PI * innerCircleRadii;
    const perc = percentage <= 100 ? percentage : 100;
    const circumferencePercentage = circumference * perc * 0.01;

    return {
      circumference,
      circumferencePercentage,
      percentDiff: circumference - circumferencePercentage,
    };
  }, [percentage, innerCircleRadii]);

  const animatedStrokeDashOffset = useAnimatedProps(() => {
    return {
      strokeDashoffset: withTiming(progress.value, {
        duration,
      }),
    };
  }, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    progress.value = getCircumferenceData.percentDiff;
  }, [percentage, innerCircleRadii, getCircumferenceData, progress]);

  return (
    <Svg
      width={loaderRadius * 2}
      height={loaderRadius * 2}
      viewBox={`0 0 ${loaderRadius * 2} ${loaderRadius * 2}`}
      fill={'none'}
    >
      <Circle cx={radius} cy={radius} r={innerCircleRadii} stroke={'#D9D9D966'} strokeWidth={borderWidth} />

      <CircleAnimated
        cx={radius}
        cy={radius}
        r={innerCircleRadii}
        stroke='#82CD47'
        strokeWidth={borderWidth}
        fill='transparent'
        animatedProps={animatedStrokeDashOffset}
        strokeDasharray={getCircumferenceData.circumference}
        strokeDashoffset={getCircumferenceData.circumference}
        strokeLinecap='round'
        transform={`rotate(-90 ${radius} ${radius})`}
      />

      <Text
        x={innerCircleRadii}
        y={innerCircleRadii}
        fontSize='12'
        fill='#fff'
        textAnchor='middle'
        alignmentBaseline='middle'
      >
        {innerText}
      </Text>
    </Svg>
  );
};
