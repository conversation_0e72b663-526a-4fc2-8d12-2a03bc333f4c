import { useMutation, useQueryClient } from '@tanstack/react-query';
import * as SecureStore from 'expo-secure-store';
import QuickCrypto from 'react-native-quick-crypto';
import { queryKeys } from '@/utils/queryKeys';

export const PASS_CODE_STORE_KEY = 'geoSafePassCode';

export const useSetupPin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (pin: string) => {
      const hashingPassCode = QuickCrypto.createHash('sha256').update(pin).digest('hex');
      await SecureStore.setItemAsync(PASS_CODE_STORE_KEY, hashingPassCode);

      return hashingPassCode;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({ queryKey: queryKeys.setupPin });
    },
  });
};
