import { useQuery } from '@tanstack/react-query';
import * as SecureStore from 'expo-secure-store';
import { queryKeys } from '@/utils/queryKeys';
import { BIOMETRIC_STORE_KEY } from './useSetupBiometric';

export const useGetBiometric = () => {
  return useQuery({
    queryKey: queryKeys.biometrics,
    queryFn: async () => {
      const biometricEnabled = await SecureStore.getItemAsync(BIOMETRIC_STORE_KEY);
      return biometricEnabled === 'true';
    },
  });
};
