import { useMutation, useQueryClient } from '@tanstack/react-query';
import { GeoSafeAbi } from '@/abis/geosafe';
import { env } from '@/utils/env';
import { queryKeys } from '@/utils/queryKeys';
import { getPublicClient } from '@/utils/web3';

type Payload = {
  safeAddress: string;
};

export const useGetSafeConfig = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ safeAddress }: Payload) => {
      const client = getPublicClient();
      const safeConfig = await client.readContract({
        address: env.GEOSAFE_CONTRACT_ADDRESS,
        abi: GeoSafeAbi,
        functionName: 'getSafeConfig',
        args: [safeAddress],
      });

      return safeConfig;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({ queryKey: queryKeys.setupPin });
    },
  });
};
