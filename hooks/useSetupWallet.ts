import { useMutation, useQueryClient } from '@tanstack/react-query';
import * as SecureStore from 'expo-secure-store';
import { encodeWallet } from '@/utils/genWallet';
import { queryKeys } from '@/utils/queryKeys';

export const PRIVATE_KEY_STORE_KEY = 'geoSafePrivateKey';
export const SEED_PHRASE_STORE_KEY = 'geoSafeSeedPhrase';

type Payload = {
  privateKey: string;
  seedPhrase: string;
  recoveryKey: string;
};

export const useSetupWallet = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: Payload) => {
      const { privateKey, seedPhrase, recoveryKey } = payload;
      const { privateKeyHash, seedPhraseHash } = encodeWallet(recoveryKey, privateKey, seedPhrase);

      await Promise.all([
        SecureStore.setItemAsync(PRIVATE_KEY_STORE_KEY, privateKeyHash),
        SecureStore.setItemAsync(SEED_PHRASE_STORE_KEY, seedPhraseHash),
      ]);

      return true;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({ queryKey: queryKeys.wallet });
    },
  });
};
