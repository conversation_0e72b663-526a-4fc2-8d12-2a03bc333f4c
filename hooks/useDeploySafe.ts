import Safe from '@safe-global/protocol-kit';
import { useMutation } from '@tanstack/react-query';
import { PrivateKeyAccount } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { env } from '@/utils/env';
import { bigIntReplacer, getWalletClient } from '@/utils/web3';

type Payload = {
  protocolKit: Safe;
  signer: PrivateKeyAccount;
};

export const useDeploySafe = () => {
  return useMutation({
    mutationFn: async ({ protocolKit, signer }: Payload) => {
      const safeDeploymentTransaction = await protocolKit.createSafeDeploymentTransaction();
      const walletClient = getWalletClient(signer);
      const authorization = await walletClient.signAuthorization({
        account: signer,
        contractAddress: safeDeploymentTransaction.to,
      });
      const serializedAuth = JSON.stringify(authorization, bigIntReplacer);
      const url = env.API_URL + '/api/v1/recovery/execute-sponsor';

      const { data: hash } = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transaction: safeDeploymentTransaction.data,
          authorization: serializedAuth,
          to: safeDeploymentTransaction.to,
        }),
      }).then((res) => res.json());

      const receipt = await waitForTransactionReceipt(walletClient, { hash, confirmations: 1 });

      return receipt;
    },
  });
};
