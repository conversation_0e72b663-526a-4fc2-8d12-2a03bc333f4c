import { useQuery } from '@tanstack/react-query';
import * as SecureStore from 'expo-secure-store';
import { queryKeys } from '@/utils/queryKeys';
import { RECOVERY_KEYS_NEW_WALLET_STORE_KEY } from './useSaveRecoveryKeysNewWallet';

export const useGetRecoveryKeysNewWallet = () => {
  return useQuery({
    queryKey: queryKeys.recoveryKeysNewWallet,
    queryFn: async () => {
      const recoveryKeysJSON = await SecureStore.getItemAsync(RECOVERY_KEYS_NEW_WALLET_STORE_KEY);
      return recoveryKeysJSON ? (JSON.parse(recoveryKeysJSON) as string[]) : [];
    },
  });
};
