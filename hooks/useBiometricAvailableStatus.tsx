import { useQuery } from '@tanstack/react-query';
import * as LocalAuthentication from 'expo-local-authentication';
import { queryKeys } from '@/utils/queryKeys';

export const useBiometricAvailableStatus = () => {
  return useQuery({
    queryKey: queryKeys.biometricsStatus,
    queryFn: async () => {
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      const bioAvailable = await LocalAuthentication.hasHardwareAsync();
      const bioEnrolled = await LocalAuthentication.isEnrolledAsync();

      return {
        bioAvailable,
        bioEnrolled,
        supportedTypes,
      };
    },
  });
};
