import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Address, encodeFunctionData, encodePacked, Hex, keccak256, PrivateKeyAccount, publicActions } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { GeoSafeAbi } from '@/abis/geosafe';
import { env } from '@/utils/env';
import { queryKeys } from '@/utils/queryKeys';
import { getAuthorization, getWalletClient } from '@/utils/web3';

export const DEFAULT_VALIDITY_BLOCKS = 6 * 30 * 24 * 60 * 4; // ~6 months

export enum RecoveryPurpose {
  ADD_SIGNER = 0, // Can only add new signer
  ADD_RECOVERY_KEYS = 1, // Can only add new recovery keys
  BOTH = 2, // Can do both operations
}

export type TRecoveryKey = {
  keyId: Hex;
  safe: Address;
  expectedAddress: Address;
  validUntilBlock: bigint;
  isUsed: boolean;
  purpose: number;
  hint: string;
  stretchedCount: number;
};

type Payload = {
  safeAddress: Address;
  keys: TRecoveryKey[];
  signer: PrivateKeyAccount;
};

export const useAddRecoveryKeys = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ keys, safeAddress, signer }: Payload) => {
      const walletClient = getWalletClient(signer);
      const currentBlockNumber = await walletClient.extend(publicActions).getBlockNumber();

      const keysMap = keys.map((key) => ({
        ...key,
        validUntilBlock: currentBlockNumber + BigInt(DEFAULT_VALIDITY_BLOCKS),
      }));

      const safeHash = keccak256(encodePacked(['address'], [safeAddress]));

      const signature = await walletClient.signMessage({
        account: signer,
        message: {
          raw: safeHash,
        },
      });

      const txData = encodeFunctionData({
        abi: GeoSafeAbi,
        functionName: 'addRecoveryKeys',
        args: [safeAddress, keysMap, signature],
      });

      const serializedAuth = await getAuthorization(signer, env.GEOSAFE_CONTRACT_ADDRESS);

      const url = env.API_URL + '/api/v1/recovery/execute-sponsor';
      const { data: hash } = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transaction: txData,
          authorization: serializedAuth,
          to: env.GEOSAFE_CONTRACT_ADDRESS,
        }),
      }).then((res) => res.json());

      if (!hash) throw new Error('No hash returned');

      const receipt = await waitForTransactionReceipt(walletClient, { hash, confirmations: 1 });

      return receipt;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({ queryKey: queryKeys.setupPin });
    },
  });
};
