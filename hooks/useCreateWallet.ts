import { useMutation } from '@tanstack/react-query';
import { LocationObject } from 'expo-location';
import { genEOAWallet, geoHash, mnemonicToPK } from '@/utils/genWallet';

type Payload = {
  recoveryKey: string;
  location: LocationObject;
};

export type TWallet = {
  privateKey: string;
  seedPhrase: string;
  stretchingCount: number;
};

export const useCreateWallet = () => {
  return useMutation<TWallet | null, Error, Payload>({
    mutationFn: async ({ recoveryKey, location }: Payload) => {
      const locationHash = geoHash(location?.coords.latitude, location?.coords.longitude, 7);
      const stretchingCount = __DEV__ ? 10 : 100_000;

      const wallet = await genEOAWallet({
        locationHash,
        recoveryKey,
        iterations: stretchingCount,
      });

      if (!wallet?.seedPhrase) return null;

      const privateKeyHex = mnemonicToPK(wallet.seedPhrase);

      return {
        privateKey: privateKeyHex,
        seedPhrase: wallet?.seedPhrase,
        stretchingCount,
      };
    },
  });
};
