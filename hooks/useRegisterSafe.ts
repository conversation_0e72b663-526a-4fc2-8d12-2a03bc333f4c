import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Address, encodeFunctionData, PrivateKeyAccount } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { GeoSafeAbi } from '@/abis/geosafe';
import { env } from '@/utils/env';
import { queryKeys } from '@/utils/queryKeys';
import { bigIntReplacer, getWalletClient } from '@/utils/web3';

type Payload = {
  safeAddress: Address;
  minKeysRequired: bigint;
  signer: PrivateKeyAccount;
};

export const useRegisterSafe = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ minKeysRequired, safeAddress, signer }: Payload) => {
      const walletClient = getWalletClient(signer);
      const txData = encodeFunctionData({
        abi: GeoSafeAbi,
        functionName: 'registerSafe',
        args: [safeAddress, minKeysRequired],
      });

      const authorization = await walletClient.signAuthorization({
        account: signer,
        contractAddress: env.GEOSAFE_CONTRACT_ADDRESS,
      });

      const serializedAuth = JSON.stringify(authorization, bigIntReplacer);
      const url = env.API_URL + '/api/v1/recovery/execute-sponsor';

      const { data: hash } = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transaction: txData,
          authorization: serializedAuth,
          to: env.GEOSAFE_CONTRACT_ADDRESS,
        }),
      }).then((res) => res.json());

      const receipt = await waitForTransactionReceipt(walletClient, { hash, confirmations: 1 });

      return receipt;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({ queryKey: queryKeys.setupPin });
    },
  });
};
