import { useMutation, useQueryClient } from '@tanstack/react-query';
import * as SecureStore from 'expo-secure-store';
import { queryKeys } from '@/utils/queryKeys';

export const BIOMETRIC_STORE_KEY = 'geoSafeBiometric';

export const useSetupBiometric = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      await SecureStore.setItemAsync(BIOMETRIC_STORE_KEY, 'true');
      return true;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({ queryKey: queryKeys.biometrics });
    },
  });
};
