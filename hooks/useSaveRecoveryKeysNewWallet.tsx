import { useMutation, useQueryClient } from '@tanstack/react-query';
import * as SecureStore from 'expo-secure-store';
import { queryKeys } from '@/utils/queryKeys';

export const RECOVERY_KEYS_NEW_WALLET_STORE_KEY = 'geoSafeRecoveryKeysNewWallet';

export const useSaveRecoveryKeysNewWallet = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (recoveryKeys: string[]) => {
      await SecureStore.setItemAsync(RECOVERY_KEYS_NEW_WALLET_STORE_KEY, JSON.stringify(recoveryKeys));
      return true;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({ queryKey: queryKeys.recoveryKey });
    },
  });
};
