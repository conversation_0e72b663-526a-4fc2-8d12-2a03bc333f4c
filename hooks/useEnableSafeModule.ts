import Safe from '@safe-global/protocol-kit';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Address, encodeFunctionData, Hex, PrivateKeyAccount } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { SafeAbi } from '@/abis/safe';
import { env } from '@/utils/env';
import { queryKeys } from '@/utils/queryKeys';
import { bigIntReplacer, CURRENT_CHAIN, getWalletClient } from '@/utils/web3';

type Payload = {
  safeAddress: Address;
  signer: PrivateKeyAccount;
  protocolKit: Safe;
  ownersPk: Hex[];
};

export const useEnableSafeModule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ signer, safeAddress, protocolKit, ownersPk }: Payload) => {
      const walletClient = getWalletClient(signer);
      const transactionData = encodeFunctionData({
        abi: SafeAbi,
        functionName: 'enableModule',
        args: [env.GEOSAFE_CONTRACT_ADDRESS],
      });

      let safeEnableModuleTransaction = await protocolKit.createTransaction({
        transactions: [
          {
            to: safeAddress,
            data: transactionData,
            value: '0',
            operation: 0, // CALL
          },
        ],
      });

      const safeTxHash = await protocolKit.getTransactionHash(safeEnableModuleTransaction);

      await Promise.all(
        ownersPk.map(async (pk) => {
          const kit = await Safe.init({
            provider: CURRENT_CHAIN.rpcUrls.default.http[0],
            safeAddress,
            signer: pk,
          });
          const signatureHash = await kit.signHash(safeTxHash);
          safeEnableModuleTransaction.addSignature(signatureHash);
        })
      );

      const encodedTx = await protocolKit.getEncodedTransaction(safeEnableModuleTransaction);

      const authorization = await walletClient.signAuthorization({
        account: signer,
        contractAddress: safeAddress,
      });

      const serializedAuth = JSON.stringify(authorization, bigIntReplacer);
      const url = env.API_URL + '/api/v1/recovery/execute-sponsor';

      const { data: hash } = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transaction: encodedTx,
          authorization: serializedAuth,
          to: safeAddress,
        }),
      }).then((res) => res.json());

      const receipt = await waitForTransactionReceipt(walletClient, { hash, confirmations: 1 });
      return receipt;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({ queryKey: queryKeys.setupPin });
    },
  });
};
