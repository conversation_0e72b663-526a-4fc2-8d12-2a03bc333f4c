import { useQuery } from '@tanstack/react-query';
import * as SecureStore from 'expo-secure-store';
import { queryKeys } from '@/utils/queryKeys';
import { RECOVERY_KEY_STORE_KEY } from './useSetupRecoveryKey';

export const useGetRecoveryKey = () => {
  return useQuery({
    queryKey: queryKeys.recoveryKey,
    queryFn: async () => {
      return await SecureStore.getItemAsync(RECOVERY_KEY_STORE_KEY);
    },
  });
};
