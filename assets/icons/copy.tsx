import Svg, { <PERSON><PERSON><PERSON><PERSON>, De<PERSON>, G, <PERSON>, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function CopyIcon({ color = '#fff', size = 20, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 20 20' fill='none' {...props}>
      <G clipPath='url(#clip0_2225_3314)'>
        <Path
          d='M13.333 13.333v2.334c0 .933 0 1.4-.181 1.756-.16.314-.415.569-.729.729-.356.181-.823.181-1.757.181H4.333c-.933 0-1.4 0-1.756-.181a1.666 1.666 0 01-.729-.729c-.181-.356-.181-.823-.181-1.756V9.333c0-.933 0-1.4.181-1.756.16-.314.415-.569.729-.729.356-.181.823-.181 1.756-.181h2.333m2.667 6.666h6.333c.934 0 1.4 0 1.757-.181.314-.16.569-.415.729-.729.181-.356.181-.823.181-1.756V4.333c0-.933 0-1.4-.181-1.756a1.666 1.666 0 00-.729-.729c-.356-.181-.823-.181-1.756-.181H9.332c-.933 0-1.4 0-1.756.181-.314.16-.569.415-.729.729-.181.356-.181.823-.181 1.756v6.334c0 .933 0 1.4.181 1.756.16.314.415.569.729.729.356.181.823.181 1.756.181z'
          stroke={color}
          strokeWidth={2}
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </G>
      <Defs>
        <ClipPath id='clip0_2225_3314'>
          <Path fill={color} d='M0 0H20V20H0z' />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default CopyIcon;
