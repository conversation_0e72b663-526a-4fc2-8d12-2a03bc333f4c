import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function NotificationIcon({ color = '#fff', size = 56, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 56 56' fill='none' {...props}>
      <Path
        d='M25.667 9.333H18.2c-3.92 0-5.88 0-7.378.763a7 7 0 00-3.059 3.06C7 14.652 7 16.612 7 20.532v12.134c0 2.17 0 3.255.239 4.145a7 7 0 004.95 4.95c.89.238 1.974.238 4.144.238v5.45c0 1.243 0 1.864.255 2.184.222.277.558.44.913.439.409 0 .894-.39 1.865-1.166l5.566-4.453c1.137-.91 1.706-1.364 2.339-1.688a6.995 6.995 0 011.777-.623C29.745 42 30.473 42 31.928 42h3.539c3.92 0 5.88 0 7.378-.763a7 7 0 003.059-3.059c.763-1.497.763-3.457.763-7.378v-.467M46.95 9.05a7 7 0 11-9.9 9.9 7 7 0 019.9-9.9z'
        stroke={color}
        strokeWidth={4}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default NotificationIcon;
