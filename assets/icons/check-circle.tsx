import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function CheckCircleIcon({ color = '#fff', size = 56, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 125 125' fill='none' {...props}>
      <Path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M62.5 5.667C31.112 5.667 5.666 31.112 5.666 62.5c0 31.388 25.446 56.833 56.834 56.833s56.833-25.445 56.833-56.833c0-31.388-25.445-56.833-56.833-56.833zm26.903 44.986a5.167 5.167 0 00-7.306-7.306L54.75 70.693 42.903 58.847a5.167 5.167 0 00-7.307 7.306l15.5 15.5a5.167 5.167 0 007.307 0l31-31z'
        fill={color}
      />
    </Svg>
  );
}

export default CheckCircleIcon;
