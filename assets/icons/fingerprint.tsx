import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function FingerprintIcon({ color = '#fff', size = 188, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 188 188' fill='none' {...props}>
      <Path
        d='M138.984 35.54a3.612 3.612 0 01-1.788-.466c-14.92-7.694-27.82-10.957-43.284-10.957-15.387 0-29.996 3.652-43.284 10.957-1.865 1.01-4.197.31-5.285-1.554-1.01-1.866-.31-4.275 1.555-5.285 14.454-7.848 30.306-11.89 47.014-11.89 16.552 0 31.006 3.653 46.859 11.813 1.943 1.01 2.642 3.341 1.632 5.206-.7 1.399-2.021 2.176-3.419 2.176zM27.781 76.338a4.049 4.049 0 01-2.253-.7c-1.788-1.243-2.176-3.652-.933-5.44 7.693-10.879 17.485-19.427 29.141-25.41 24.4-12.59 55.64-12.667 80.119-.078 11.656 5.983 21.448 14.454 29.141 25.255 1.243 1.71.855 4.197-.933 5.44-1.787 1.243-4.196.855-5.439-.933-6.994-9.79-15.853-17.484-26.344-22.846-22.303-11.423-50.822-11.423-73.047.078-10.569 5.44-19.427 13.21-26.421 23.002-.622 1.087-1.788 1.632-3.03 1.632zm48.569 93.795c-1.01 0-2.02-.389-2.72-1.166-6.761-6.76-10.413-11.112-15.62-20.515-5.362-9.558-8.16-21.215-8.16-33.726 0-23.08 19.739-41.885 43.984-41.885 24.246 0 43.984 18.805 43.984 41.885a3.848 3.848 0 01-3.886 3.886 3.848 3.848 0 01-3.885-3.886c0-18.806-16.241-34.114-36.213-34.114-19.971 0-36.212 15.308-36.212 34.114 0 11.19 2.486 21.526 7.227 29.918 4.973 8.937 8.392 12.745 14.376 18.806a3.99 3.99 0 010 5.517c-.855.777-1.865 1.166-2.875 1.166zm55.717-14.376c-9.247 0-17.407-2.332-24.09-6.916-11.578-7.849-18.495-20.593-18.495-34.115a3.847 3.847 0 013.886-3.885 3.847 3.847 0 013.886 3.885c0 10.957 5.595 21.292 15.075 27.665 5.518 3.73 11.967 5.517 19.738 5.517 1.865 0 4.974-.233 8.082-.777 2.098-.389 4.119 1.01 4.507 3.186.389 2.098-1.01 4.119-3.186 4.507-4.429.855-8.315.933-9.403.933zm-15.619 16.008c-.311 0-.7-.078-1.01-.156C103.082 168.19 95 163.605 86.53 155.29c-10.88-10.801-16.863-25.177-16.863-40.564 0-12.589 10.724-22.847 23.934-22.847 13.211 0 23.935 10.258 23.935 22.847 0 8.315 7.227 15.076 16.163 15.076 8.937 0 16.164-6.761 16.164-15.076 0-29.296-25.256-53.076-56.34-53.076-22.07 0-42.274 12.279-51.366 31.317-3.03 6.295-4.584 13.677-4.584 21.759 0 6.061.544 15.62 5.206 28.053.777 2.021-.233 4.274-2.254 4.974-2.02.777-4.274-.311-4.973-2.254-3.808-10.18-5.673-20.282-5.673-30.773 0-9.325 1.788-17.796 5.284-25.178 10.336-21.68 33.26-35.746 58.36-35.746 35.358 0 64.111 27.276 64.111 60.846 0 12.589-10.724 22.847-23.935 22.847-13.21 0-23.934-10.258-23.934-22.847 0-8.315-7.227-15.075-16.164-15.075-8.936 0-16.163 6.76-16.163 15.075 0 13.289 5.128 25.722 14.531 35.047 7.383 7.305 14.454 11.346 25.411 14.377 2.098.544 3.264 2.719 2.72 4.74-.388 1.787-2.02 2.953-3.652 2.953z'
        fill={color}
      />
    </Svg>
  );
}

export default FingerprintIcon;
