import Svg, { <PERSON><PERSON><PERSON><PERSON>, De<PERSON>, G, <PERSON>, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function FaceIcon({ color = '#fff', size = 56, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 56 56' fill='none' {...props}>
      <G clipPath='url(#clip0_2225_3535)' fill='#fff'>
        <Path d='M2.88 15.36V9.12c0-3.525 2.715-6.24 6.24-6.24h6.24a1.44 1.44 0 100-2.88H9.12C4.005 0 0 4.005 0 9.12v6.24a1.44 1.44 0 102.88 0zM53.12 15.36V9.12c0-3.525-2.715-6.24-6.24-6.24h-6.24a1.44 1.44 0 110-2.88h6.24C51.995 0 56 4.005 56 9.12v6.24a1.44 1.44 0 11-2.88 0zM2.88 40.64v6.24c0 3.525 2.715 6.24 6.24 6.24h6.24a1.44 1.44 0 110 2.88H9.12C4.005 56 0 51.995 0 46.88v-6.24a1.44 1.44 0 112.88 0zM53.12 40.64v6.24c0 3.525-2.715 6.24-6.24 6.24h-6.24a1.44 1.44 0 100 2.88h6.24c5.115 0 9.12-4.005 9.12-9.12v-6.24a1.44 1.44 0 10-2.88 0zM15.228 21.15v4.001c0 .829.627 1.5 1.4 1.5.773 0 1.4-.671 1.4-1.5V21.15c0-.828-.627-1.5-1.4-1.5-.773 0-1.4.672-1.4 1.5zM38.316 21.15v4.001c0 .829.627 1.5 1.4 1.5.773 0 1.4-.671 1.4-1.5V21.15c0-.828-.627-1.5-1.4-1.5-.773 0-1.4.672-1.4 1.5zM18.152 41.358c2.731 2.549 6.038 3.835 9.848 3.835 3.81 0 7.117-1.286 9.848-3.835a1.474 1.474 0 10-2.011-2.154c-2.182 2.035-4.77 3.042-7.837 3.042-3.068 0-5.655-1.007-7.837-3.043a1.474 1.474 0 10-2.01 2.155zM28 21.123v10.316c0 .66-.323.982-.986.982h-.979a1.474 1.474 0 000 2.947h.979c2.29 0 3.933-1.64 3.933-3.93V21.124a1.474 1.474 0 00-2.947 0z' />
      </G>
      <Defs>
        <ClipPath id='clip0_2225_3535'>
          <Path fill='#fff' d='M0 0H56V56H0z' />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default FaceIcon;
