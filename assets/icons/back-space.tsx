import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function BackSpaceIcon({ color = '#fff', size = 24, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <Path
        d='M18.75 4a3.25 3.25 0 013.245 3.066L22 7.25v9.5a3.25 3.25 0 01-3.066 3.245L18.75 20h-8.5a3.25 3.25 0 01-2.086-.756l-.155-.139-4.995-4.75a3.25 3.25 0 01-.116-4.594l.116-.116 4.995-4.75a3.25 3.25 0 012.032-.888l.21-.007h8.5zm-7.304 4.397a.75.75 0 00-1.049 1.05l.073.083L12.94 12l-2.47 2.47-.073.084a.75.75 0 001.05 1.049l.083-.073L14 13.061l2.47 2.47.084.072a.75.75 0 001.05-1.05l-.074-.083L15.061 12l2.47-2.47.072-.084a.75.75 0 00-1.05-1.049l-.083.073L14 10.939l-2.47-2.47-.084-.072z'
        fill='#fff'
      />
    </Svg>
  );
}

export default BackSpaceIcon;
