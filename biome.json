{"$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**", "!dist", "!build", "!out", "!node_modules", "!android", "!ios", "!scripts", "!expo-env.d.ts", "!.expo"]}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 120, "attributePosition": "auto", "bracketSpacing": true}, "linter": {"enabled": true, "rules": {"a11y": "on", "recommended": false, "complexity": {"noBannedTypes": "off", "noExtraBooleanCast": "error", "noUselessCatch": "error", "noUselessThisAlias": "error", "noUselessTypeConstraint": "error", "noForEach": "error", "noStaticOnlyClass": "error", "noUselessSwitchCase": "error", "useFlatMap": "error"}, "correctness": {"useValidTypeof": "error", "noChildrenProp": "error", "noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInnerDeclarations": "error", "noInvalidConstructorSuper": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "off", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "warn", "noUnusedVariables": {"level": "error", "fix": "safe", "options": {}}, "useIsNan": "error", "useJsxKeyInIterable": "error", "useValidForDirection": "error", "useYield": "error", "noUnusedImports": {"level": "error", "fix": "safe", "options": {}}, "useExhaustiveDependencies": "error"}, "security": {"noDangerouslySetInnerHtmlWithChildren": "error", "noBlankTarget": "error"}, "style": {"noNegationElse": "off", "useForOf": "error", "useNodejsImportProtocol": "error", "useNumberNamespace": "error", "useArrayLiterals": "off", "noNamespace": "error", "useAsConstAssertion": "error", "useBlockStatements": "off", "useImportType": "off"}, "nursery": {"useSortedClasses": {"level": "error", "fix": "safe", "options": {}}}, "suspicious": {"noDoubleEquals": "error", "noThenProperty": "error", "useIsArray": "error", "noIrregularWhitespace": "error", "noWith": "error", "noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCommentText": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateJsxProps": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "off", "noExplicitAny": "off", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "off", "noRedeclare": "error", "noShadowRestrictedNames": "off", "noSparseArray": "error", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "useGetterReturn": "error"}}}, "javascript": {"formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "jsxQuoteStyle": "single", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto", "bracketSpacing": true, "lineWidth": 120, "lineEnding": "lf"}, "parser": {"unsafeParameterDecoratorsEnabled": true}}, "overrides": [{"javascript": {"formatter": {"trailingCommas": "none"}}}, {"javascript": {"globals": ["after<PERSON>ach", "afterAll", "beforeEach", "beforeAll", "describe", "expect", "it", "test", "jest"]}}]}