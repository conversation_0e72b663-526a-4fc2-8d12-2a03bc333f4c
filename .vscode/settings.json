{"editor.codeActionsOnSave": {"source.organizeImports.biome": "always", "source.fixAll.biome": "explicit", "source.sortMembers": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "typescript.tsdk": "node_modules/typescript/lib", "editor.quickSuggestions": {"strings": "on"}, "typescript.preferences.autoImportSpecifierExcludeRegexes": ["^(node:)?os$"]}