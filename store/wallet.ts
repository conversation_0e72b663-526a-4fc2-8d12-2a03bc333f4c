import { createSelectorFunctions } from 'auto-zustand-selectors-hook';
import { Address } from 'viem';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { zustandStorage } from './storage';

export interface IWalletStore {
  safeWallet: Address | null;
  setSafeWallet: (address: Address) => void;
}

const useBaseWalletStore = create<IWalletStore>()(
  persist(
    (set) => ({
      safeWallet: null,
      setSafeWallet: (address) => {
        set((state) => ({ ...state, safeWallet: address }));
      },
    }),
    {
      name: 'wallet-store',
      storage: createJSONStorage(() => zustandStorage),
      version: 1,
    }
  )
);

export const useWalletStore = createSelectorFunctions(useBaseWalletStore);
