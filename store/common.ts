import { createSelectorFunctions } from 'auto-zustand-selectors-hook';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { zustandStorage } from './storage';

export interface ICommonStore {
  isFirstTime: boolean;
  setIsFirstTime: (status: boolean) => void;
}

const useBaseCommonStore = create<ICommonStore>()(
  persist(
    (set) => ({
      isFirstTime: true,
      setIsFirstTime: (status) => {
        set((state) => ({ ...state, isFirstTime: status }));
      },
    }),
    {
      name: 'common-store',
      storage: createJSONStorage(() => zustandStorage),
      // partialize: (state) => state,
      // merge: (persistedState, currentState) => {
      //   return {
      //     ...currentState,
      //     ...(persistedState as ICommonStore),
      //   };
      // },
      version: 1,
    }
  )
);

export const useCommonStore = createSelectorFunctions(useBaseCommonStore);
