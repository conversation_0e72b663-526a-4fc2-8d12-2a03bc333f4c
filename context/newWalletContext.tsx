import { createSafeContext } from '@/utils/createSafeContext';

type NewWalletContextProps = {};

export const [NewWalletContextProvider, useNewWalletContext] = createSafeContext<NewWalletContextProps>(
  'NewWalletContextProvider component was not found in tree'
);

export const NewWalletWrapper = ({ children }: { children: React.ReactNode }) => {
  const value = {};

  return <NewWalletContextProvider value={value}>{children}</NewWalletContextProvider>;
};
