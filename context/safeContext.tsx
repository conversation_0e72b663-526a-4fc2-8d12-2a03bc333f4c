import Safe from '@safe-global/protocol-kit';
import { createSafeContext } from '@/utils/createSafeContext';

type SafeContextProps = {
  onSetSigners: (signers: string[]) => void;
  onSetThreshold: (threshold: number) => void;
  safeInstance: Safe | null;
  isLoading: boolean;
};

export const [SafeContextProvider, useSafeContext] = createSafeContext<SafeContextProps>(
  'SafeContextProvider component was not found in tree'
);
