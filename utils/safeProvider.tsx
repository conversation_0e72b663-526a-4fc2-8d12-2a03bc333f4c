import Safe from '@safe-global/protocol-kit';
import { PropsWithChildren, useState } from 'react';
import { Account, Chain, Hex, Transport, WaitForTransactionReceiptReturnType, WalletClient } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { SafeContextProvider } from '@/context/safeContext';
import { useGetWallet } from '@/hooks/useGetWallet';
import { useWalletStore } from '@/store/wallet';

type Props = {
  initialSafeAddress?: string;
};

export const SafeProvider = ({ children, initialSafeAddress }: PropsWithChildren<Props>) => {
  const [safeInstance, _setSafeInstance] = useState<Safe | null>(null);
  const [_signers, setSigners] = useState<string[]>([]);
  const [_threshold, setThreshold] = useState<number>(0);
  const [isLoading, _setIsLoading] = useState(false);

  const { data: wallet, isPending } = useGetWallet();
  const _safeWallet = useWalletStore.use.safeWallet();

  const handleSetSigners = (signers: string[]) => {
    setSigners(signers);
  };

  async function _deploySafe(protocolKit: Safe): Promise<WaitForTransactionReceiptReturnType<Chain>> {
    const safeDeploymentTransaction = await protocolKit.createSafeDeploymentTransaction();

    const signer = (await protocolKit.getSafeProvider().getExternalSigner()) as WalletClient<Transport, Chain, Account>;
    const client = protocolKit.getSafeProvider().getExternalProvider();

    if (!signer) throw new Error('SafeProvider must be initialized with a signer to use this function');

    const hash = await signer.sendTransaction({
      to: safeDeploymentTransaction.to as `0x${string}`,
      data: safeDeploymentTransaction.data as Hex,
      value: BigInt(safeDeploymentTransaction.value),
      account: signer.account,
    });

    const receipt = await waitForTransactionReceipt(client, { hash });

    return receipt;
  }

  // useEffect(() => {
  //   if (!signers && !safeWallet) return;

  //   (async () => {
  //     // const SIGNER_PRIVATE_KEY = `0x${wallet.privateKey}` as const;
  //     // const _signerWalletAddress = privateKeyToAddress(SIGNER_PRIVATE_KEY);

  //     try {
  //       setIsLoading(true);
  //       const predictedSafe: PredictedSafeProps = {
  //         safeAccountConfig: {
  //           owners: signers,
  //           threshold,
  //         },
  //         // safeDeploymentConfig: {
  //         //   saltNonce: '1',
  //         // },
  //       };

  //       const initSafe =
  //         signers.length > 0 && threshold > 0
  //           ? {
  //               provider: sepolia.rpcUrls.default.http[0],
  //               predictedSafe: predictedSafe,
  //             }
  //           : {
  //               safeAddress: '******************************************',
  //               provider: sepolia.rpcUrls.default.http[0],
  //             };
  //       const protocolKitInstance = await Safe.init(initSafe);

  //       const safeAddress = await protocolKitInstance.getAddress();
  //       const isDeployed = await protocolKitInstance.isSafeDeployed();

  //       console.log('Safe address', safeAddress);
  //       console.log('Is deployed', isDeployed);

  //       setSafeInstance(protocolKitInstance);
  //     } catch (error) {
  //       console.error(error);
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   })();
  // }, [signers, safeWallet, threshold]);

  if (isPending) return null;

  return (
    <SafeContextProvider
      value={{
        onSetSigners: handleSetSigners,
        onSetThreshold: setThreshold,
        safeInstance,
        isLoading,
      }}
    >
      {children}
    </SafeContextProvider>
  );
};
