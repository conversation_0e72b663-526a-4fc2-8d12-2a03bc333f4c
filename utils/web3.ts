import { Address, createPublicClient, createWalletClient, Hex, http, PrivateKeyAccount } from 'viem';
import { privateKeyToAccount } from 'viem/accounts';
import { sepolia } from 'viem/chains';

export const CURRENT_CHAIN = sepolia;

export const pkToAccount = (privateKey: Hex) => {
  const signer = privateKeyToAccount(privateKey);
  return signer;
};

export const getWalletClient = (account?: PrivateKeyAccount) =>
  createWalletClient({
    account,
    chain: CURRENT_CHAIN,
    transport: http(),
  });

export const getPublicClient = () =>
  createPublicClient({
    chain: CURRENT_CHAIN,
    transport: http(),
  });

export const bigIntReplacer = (_key: string, value: any) => (typeof value === 'bigint' ? value.toString() : value);

export const getAuthorization = async (signer: PrivateKeyAccount, contractAddress: Address) => {
  const walletClient = getWalletClient(signer);
  const authorization = await walletClient.signAuthorization({
    account: signer,
    contractAddress,
  });

  const serializedAuth = JSON.stringify(authorization, bigIntReplacer);
  return serializedAuth;
};
