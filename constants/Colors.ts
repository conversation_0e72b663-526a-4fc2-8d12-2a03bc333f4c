/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const _tintColorLight = '#0a7ea4';
const tintColorDark = '#fff';

export const Colors = {
  light: {
    text: '#ECEDEE',
    background: '#15151F',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
    primary: '#23C5A2',
    secondary: '#29293D',
    textButtonPrimary: '#0C0C12',
    white04: 'rgba(255, 255, 255, 0.04)',
    white05: 'rgba(255, 255, 255, 0.05)',
    white15: 'rgba(255, 255, 255, 0.15)',
    white35: 'rgba(255, 255, 255, 0.35)',
    white50: 'rgba(255, 255, 255, 0.50)',
    white65: 'rgba(255, 255, 255, 0.65)',
    white90: 'rgba(255, 255, 255, 0.90)',

    'Neutrals/800': '#21212A',
    'Blue/500': '#5FF77B',
    'Text/Neutral/Tertiary': '#767676',
  },
  dark: {
    text: '#ECEDEE',
    background: '#15151F',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
    primary: '#23C5A2',
    secondary: '#29293D',
    textButtonPrimary: '#0C0C12',
    white04: 'rgba(255, 255, 255, 0.04)',
    white05: 'rgba(255, 255, 255, 0.05)',
    white15: 'rgba(255, 255, 255, 0.15)',
    white35: 'rgba(255, 255, 255, 0.35)',
    white50: 'rgba(255, 255, 255, 0.50)',
    white65: 'rgba(255, 255, 255, 0.65)',
    white90: 'rgba(255, 255, 255, 0.90)',

    'Neutrals/800': '#21212A',
    'Blue/500': '#5FF77B',
    'Text/Neutral/Tertiary': '#767676',
  },
};

export const FontWeight = {
  light: {
    fontWeight: '300',
    fontFamily: 'Poppins_300Light',
  },
  regular: {
    fontWeight: '400',
    fontFamily: 'Poppins_400Regular',
  },
  medium: {
    fontWeight: '500',
    fontFamily: 'Poppins_500Medium',
  },
  semiBold: {
    fontWeight: '600',
    fontFamily: 'Poppins_600SemiBold',
  },
  bold: {
    fontWeight: '700',
    fontFamily: 'Poppins_700Bold',
  },
} as const;
